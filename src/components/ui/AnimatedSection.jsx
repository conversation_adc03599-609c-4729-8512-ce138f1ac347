import { motion } from "framer-motion"

const AnimatedSection = ({ children, className = "" }) => {
  // Animation variants for scroll-driven animations
  const sectionVariants = {
    hidden: {
      opacity: 0,
      y: 50, // Start slightly below
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94] // Custom easing for smooth animation
      }
    },
    visible: {
      opacity: 1,
      y: 0, // Move to original position
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94] // Custom easing for smooth animation
      }
    },
    exit: {
      opacity: 0,
      y: -50, // Move slightly up when exiting
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94] // Custom easing for smooth animation
      }
    }
  }

  return (
    <motion.div
      className={className}
      initial="hidden"
      whileInView="visible"
      exit="exit"
      variants={sectionVariants}
      viewport={{
        once: false, // Allow animation to trigger multiple times
        amount: 0.3, // Trigger when 30% of the section is visible
        margin: "-100px 0px -100px 0px" // Add margin to trigger animation earlier
      }}
    >
      {children}
    </motion.div>
  )
}

export { AnimatedSection }
