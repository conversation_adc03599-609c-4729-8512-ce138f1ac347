import { LayoutGroup, motion } from "framer-motion"

import RotatingText from "@/components/ui/rotating-text"

const AboutMe = () => {
  const words = ["Full Stack", "Django", "React", "Nerdy"]

  const transition = {
    type: "spring",
    damping: 30,
    stiffness: 400
  }

  return (
    <section
      id="about_me"
      className="section flex flex-col items-center justify-center gap-2 px-4 !pt-0 sm:gap-3 sm:px-6 md:gap-4 md:px-8"
    >
      <div className="text-center">
        <span className="text-lg sm:text-xl md:text-2xl">
          Hello, I am Jaimish Trivedi
        </span>
      </div>
      <div className="w-full max-w-7xl">
        <LayoutGroup>
          <motion.div
            className="flex flex-col items-center justify-center gap-2 text-center text-lg text-white sm:flex-row sm:gap-3 sm:text-left md:gap-4"
            layout
          >
            <RotatingText
              texts={words}
              mainClassName="px-2 sm:px-3 md:px-4 lg:px-5 text-4xl sm:text-6xl md:text-7xl lg:text-8xl bg-primary text-black overflow-hidden py-1 sm:py-1.5 md:py-2 lg:py-2.5 justify-center rounded-lg sm:rounded-xl flex-shrink-0"
              staggerFrom={"last"}
              initial={{ y: "100%" }}
              animate={{ y: 0 }}
              exit={{ y: "-120%" }}
              staggerDuration={0.025}
              splitLevelClassName="pb-0.5 sm:pb-1"
              transition={transition}
              rotationInterval={2000}
            />
            <motion.span
              className="whitespace-nowrap pt-1 text-4xl sm:pt-1.5 sm:text-6xl md:pt-2 md:text-7xl lg:pt-2.5 lg:text-8xl"
              layout
              transition={transition}
            >
              Developer
            </motion.span>
          </motion.div>
        </LayoutGroup>
      </div>
    </section>
  )
}

export default AboutMe
